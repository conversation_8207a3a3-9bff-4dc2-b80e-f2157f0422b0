# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.project
.pydevproject
.settings/

# Jupyter Notebook
.ipynb_checkpoints

# API keys and secrets
.env.local
.env.development.local
.env.test.local
.env.production.local
config.local.py
core/config/local.py

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
core/logs/

# Database
*.db
*.sqlite3
*.sqlite

# Cache
.cache/
.pytest_cache/
nosetests.xml
coverage.xml
*.cover
core/data/cache/
core/data/temp/

# Distribution / packaging
build/
dist/
*.egg-info/
.installed.cfg

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
coverage.xml

# Project specific
data/cache/
data/temp/

# Duplicate repository
ai-asset-eval-team-repo/
