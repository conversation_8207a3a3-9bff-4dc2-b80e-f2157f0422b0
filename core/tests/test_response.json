{"normal_data": "This is normal data", "timestamp": "2025-01-01T00:00:00", "nested": {"timestamp": "2025-01-02T00:00:00", "normal": "This is normal nested data"}, "dataframe": {"_metadata": {"columns": ["A", "B"], "shape": [5, 2], "index_type": "<class 'pandas.core.indexes.datetimes.DatetimeIndex'>"}, "index": ["2025-01-01T00:00:00", "2025-01-02T00:00:00", "2025-01-03T00:00:00", "2025-01-04T00:00:00", "2025-01-05T00:00:00"], "A": [1.0753940271641707, -0.42220914240787644, -1.1982178508183652, -0.7980288270095263, 0.9767018906167966], "B": [0.9684500484523018, -1.5398161046297842, -1.9709980229049662, 0.03344480902106626, -1.5039699346553537]}}