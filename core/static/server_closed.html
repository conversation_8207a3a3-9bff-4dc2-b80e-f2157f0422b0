<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>AI Finance Dashboard - Server Closed</title>
    <style>
        :root {
            --primary-color: #4f46e5; /* Indigo */
            --primary-light: #818cf8; /* Light indigo */
            --primary-dark: #3730a3; /* Dark indigo */
            --bg-dark: #1f2937; /* Dark gray/blue - main background */
            --text-primary: #f9fafb; /* Almost white - primary text */
            --text-secondary: #e5e7eb; /* Light gray - secondary text */
            --danger-color: #ef4444; /* Red */
        }

        body {
            font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-dark);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            font-size: 16px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 600px;
            padding: 2rem;
        }

        h1 {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            color: var(--text-secondary);
        }

        .server-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 2rem;
            position: relative;
        }

        .server-icon:before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--danger-color);
            border-radius: 50%;
            opacity: 0.2;
            animation: pulse 2s infinite;
        }

        .server-icon:after {
            content: "✕";
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            font-size: 40px;
            color: var(--danger-color);
        }

        @keyframes pulse {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
            }
            
            70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
            }
            
            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
            }
        }

        .restart-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 1rem;
        }

        .restart-button:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(79, 70, 229, 0.4);
        }

        .instructions {
            margin-top: 2rem;
            padding: 1rem;
            background-color: rgba(79, 70, 229, 0.1);
            border-radius: 5px;
            text-align: left;
        }

        .instructions h2 {
            font-size: 1.2rem;
            color: var(--primary-color);
            margin-top: 0;
        }

        .instructions ol {
            margin-left: 1.5rem;
            padding-left: 0;
        }

        .instructions li {
            margin-bottom: 0.5rem;
        }

        .code {
            background-color: rgba(0, 0, 0, 0.3);
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Server Closed</h1>
        <div class="server-icon"></div>
        <p>The AI Finance Dashboard server has been shut down.</p>
        <p>You need to restart the server to continue using the dashboard.</p>
        
        <button class="restart-button" onclick="window.location.reload()">Check Server Status</button>
        
        <div class="instructions">
            <h2>How to restart the server:</h2>
            <ol>
                <li>Open your terminal or command prompt</li>
                <li>Navigate to the project directory</li>
                <li>Run one of the following commands:
                    <ul>
                        <li><span class="code">python start_dashboard.py</span></li>
                        <li>or <span class="code">./run_streamlit.sh</span></li>
                        <li>or <span class="code">streamlit run dashboard.py</span></li>
                    </ul>
                </li>
                <li>Once the server is running, refresh this page</li>
            </ol>
        </div>
    </div>

    <script>
        // Check if the server is back online periodically
        setInterval(() => {
            fetch(window.location.href, {
                method: 'HEAD',
                cache: 'no-store'
            })
            .then(response => {
                if (response.ok) {
                    // If we get a successful response, check if it's the Streamlit app
                    fetch(window.location.href)
                        .then(response => response.text())
                        .then(html => {
                            if (html.includes('streamlit-root')) {
                                // Streamlit is loaded, redirect
                                window.location.reload();
                            }
                        })
                        .catch(err => console.error('Error checking Streamlit content:', err));
                }
            })
            .catch(err => {
                console.log('Server still offline:', err);
            });
        }, 5000); // Check every 5 seconds
    </script>
</body>
</html>
