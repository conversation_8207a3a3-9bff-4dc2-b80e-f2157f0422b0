<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Plotly Test</title>
    <script src="https://cdn.plot.ly/plotly-2.29.1.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>Simple Plotly Test Page</h1>
    <p>This page tests if <PERSON>lot<PERSON> is working correctly without CSP restrictions.</p>
    
    <div id="chart1" class="chart-container"></div>
    
    <script>
        // Display Plotly version
        document.write('<p>Plotly version: ' + Plotly.version + '</p>');
        
        // Simple bar chart
        const data1 = [{
            x: ['Category A', 'Category B', 'Category C'],
            y: [20, 14, 23],
            type: 'bar'
        }];
        
        Plotly.newPlot('chart1', data1, {
            title: 'Simple Bar Chart'
        });
    </script>
</body>
</html>
