<div class="pagination">
    {% if pages > 1 %}
    <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
            <li class="page-item {% if page == 1 %}disabled{% endif %}">
                <a class="page-link" href="?page={{ page - 1 }}&page_size={{ page_size }}" aria-label="Previous">
                    <span aria-hidden="true">«</span>
                </a>
            </li>
            
            {% for p in range(1, pages + 1) %}
                {% if p == page %}
                <li class="page-item active"><span class="page-link">{{ p }}</span></li>
                {% elif p <= 3 or p >= pages - 2 or (p >= page - 1 and p <= page + 1) %}
                <li class="page-item"><a class="page-link" href="?page={{ p }}&page_size={{ page_size }}">{{ p }}</a></li>
                {% elif p == 4 and page > 5 or p == pages - 3 and page < pages - 4 %}
                <li class="page-item disabled"><span class="page-link">...</span></li>
                {% endif %}
            {% endfor %}
            
            <li class="page-item {% if page == pages %}disabled{% endif %}">
                <a class="page-link" href="?page={{ page + 1 }}&page_size={{ page_size }}" aria-label="Next">
                    <span aria-hidden="true">»</span>
                </a>
            </li>
        </ul>
    </nav>
    <div class="text-center text-muted">
        Showing {{ (page - 1) * page_size + 1 }} to {{ min(page * page_size, total) }} of {{ total }} items
    </div>
    {% endif %}
</div>
