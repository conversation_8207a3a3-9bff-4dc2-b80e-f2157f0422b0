{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>{{ title }}</h1>
    
    <div class="row">
        {% for article in articles.items %}
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                {% if article.urlToImage %}
                <img src="{{ article.urlToImage }}" class="card-img-top" alt="{{ article.title }}">
                {% endif %}
                <div class="card-body">
                    <h5 class="card-title">{{ article.title }}</h5>
                    <p class="card-text">{{ article.description }}</p>
                    <p class="card-text"><small class="text-muted">{{ article.publishedAt }}</small></p>
                    <a href="{{ article.url }}" target="_blank" class="btn btn-primary">Read More</a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    {% include "components/pagination.html" %}
</div>
{% endblock %}
