<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>{{ title }} - Financial Analysis Dashboard</title>
    <link rel="stylesheet" href="/static/css/styles.css">
    <script src="https://cdn.plot.ly/plotly-2.29.1.min.js"></script>
    <script src="/static/js/chart-utils.js"></script>
    <style>
        body {
            background-color: var(--bg-dark);
            color: var(--text-primary);
        }

        .chart-container {
            width: 100%;
            height: 600px;
            margin: 20px 0;
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--box-shadow);
        }

        .chart-title {
            font-size: 1.5em;
            margin-bottom: 10px;
            color: var(--text-primary);
        }

        .chart-description {
            margin-bottom: 20px;
            color: var(--text-secondary);
        }

        .data-source {
            font-size: 0.8em;
            color: var(--text-muted);
            margin-top: 5px;
        }

        .insights {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--box-shadow);
        }

        .insights h2 {
            color: var(--text-primary);
            margin-top: 0;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }

        .insights ul {
            list-style-type: none;
            padding: 0;
        }

        .insights li {
            padding: 10px;
            border-left: 4px solid var(--primary-color);
            background-color: var(--bg-input);
            margin-bottom: 10px;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }

        .comparison-table th {
            background-color: var(--bg-input);
            color: var(--text-primary);
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .comparison-table td {
            padding: 10px;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-secondary);
        }

        .comparison-table tr:last-child td {
            border-bottom: none;
        }

        .comparison-table tr:hover td {
            background-color: var(--bg-hover);
        }

        .metric-group {
            margin-bottom: 30px;
        }

        .metric-group h3 {
            color: var(--text-primary);
            margin-top: 0;
            margin-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 5px;
        }

        .best-value {
            color: var(--success-color);
            font-weight: bold;
        }

        .worst-value {
            color: var(--danger-color);
        }

        /* Mobile styles */
        @media (max-width: 768px) {
            body {
                padding: 0;
            }

            header {
                padding: 10px;
            }

            header h1 {
                font-size: 1.2rem;
                margin: 0 0 10px 0;
            }

            nav {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 10px;
            }

            .nav-link {
                padding: 5px 10px;
                font-size: 0.9rem;
            }

            .chart-container {
                height: 400px;
                padding: 10px;
                margin: 10px 0;
            }

            .chart-title {
                font-size: 1.2rem;
            }

            .insights {
                padding: 10px;
            }

            .insights h2 {
                font-size: 1.2rem;
            }

            .comparison-table {
                font-size: 0.9rem;
            }

            .comparison-table th,
            .comparison-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>{{ title }}</h1>
        <nav>
            <a href="/" class="nav-link">Home</a>
            <a href="javascript:history.back()" class="nav-link">Back</a>
        </nav>
    </header>

    <main>
        <div class="container">
            <div class="chart-container">
                <div class="chart-title">{{ chart_title }}</div>
                <div class="chart-description">{{ chart_description }}</div>
                <div id="chart"></div>
                <div class="data-source">Data source: {{ data_source }}</div>
            </div>

            <div class="insights">
                <h2>Key Insights</h2>
                <ul>
                    {% for insight in insights %}
                    <li>{{ insight }}</li>
                    {% endfor %}
                </ul>
            </div>

            {% if recommendations is defined and recommendations %}
            <div class="insights recommendations">
                <h2>Recommendations</h2>
                <ul>
                    {% for recommendation in recommendations %}
                    <li>{{ recommendation }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}

            <div class="comparison-data">
                {% for group_name, group_data in comparison_data.items() %}
                <div class="metric-group">
                    <h3>{{ group_name|title }}</h3>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Metric</th>
                                {% for item in items %}
                                <th>{{ item }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for metric_name, metric_values in group_data.items() %}
                            <tr>
                                <td>{{ metric_name|replace('_', ' ')|title }}</td>
                                {% for item in items %}
                                <td class="{{ get_value_class(metric_name, item, metric_values) }}">
                                    {{ format_value(metric_values.get(item)) }}
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endfor %}
            </div>
        </div>
    </main>

    <footer>
        <p>&copy; 2023 Financial Analysis Dashboard. Powered by AI Agents.</p>
    </footer>

    <script>
        // Parse the chart data from the server
        const chartData = {{ chart_json|safe }};

        // Initialize the chart using ChartUtils
        ChartUtils.initChart('chart', chartData, function(data) {
            // Custom click handler for comparison chart
            const point = data.points[0];
            console.log('Clicked comparison point:', point);

            // You can add custom behavior here for comparison charts
            // For example, show a comparison tooltip or highlight related points
        }).then(() => {
            console.log('Comparison chart initialized successfully');
        }).catch(error => {
            console.error('Error initializing comparison chart:', error);
        });
    </script>
</body>
</html>
